<?php
require 'koneksi.php';
require_once 'includes/session_manager.php';
safe_session_start();

// Ambil input dari form
$user = trim($_POST['username']);
$pass = trim($_POST['password']);

// Cek apakah input adalah email (untuk client) atau username (untuk admin/proyek)
$is_email = filter_var($user, FILTER_VALIDATE_EMAIL);

if ($is_email) {
    // Login sebagai client menggunakan email
    $sql = mysqli_query($koneksi, "SELECT * FROM users WHERE email='$user' AND role='client'");

    if (!$sql) {
        die("Query Error: " . mysqli_error($koneksi));
    }

    $cek = mysqli_num_rows($sql);

    if ($cek > 0) {
        $data = mysqli_fetch_array($sql);

        // Verifikasi password dengan hash
        if (password_verify($pass, $data['password'])) {
            // Simpan session untuk client
            $_SESSION['id_client'] = $data['id'];
            $_SESSION['user'] = $user;
            $_SESSION['nama'] = $data['first_name'] . ' ' . $data['last_name'];
            $_SESSION['level'] = 'client';
            $_SESSION['email'] = $data['email'];

            header('Location:client/client.php');
            exit;
        } else {
            echo "<script>alert('Email atau Password tidak cocok'); window.location='index.php';</script>";
            exit;
        }
    } else {
        echo "<script>alert('Email tidak ditemukan atau bukan client'); window.location='index.php';</script>";
        exit;
    }
} else {
    // Login sebagai admin/proyek menggunakan username
    $sql = mysqli_query($koneksi, "SELECT * FROM petugas WHERE username='$user' AND password='$pass'");

    if (!$sql) {
        die("Query Error: " . mysqli_error($koneksi));
    }

    $cek = mysqli_num_rows($sql);

    if ($cek > 0) {
        $data = mysqli_fetch_array($sql);

        // Simpan session untuk admin/proyek
        $_SESSION['id_petugas'] = $data['id_petugas'];
        $_SESSION['user'] = $user;
        $_SESSION['nama'] = $data['nama_petugas'];
        $_SESSION['level'] = $data['level'];

        // Arahkan sesuai level user
        if ($data['level'] == "proyek") {
            header('Location:proyek/proyek.php');
        } elseif ($data['level'] == "admin") {
            header('Location:admin/admin.php');
        } else {
            echo "<script>alert('Level tidak dikenali!'); window.location='index.php';</script>";
        }
    } else {
        echo "<script>alert('Username atau Password tidak ditemukan'); window.location='index.php';</script>";
    }
}
?>
