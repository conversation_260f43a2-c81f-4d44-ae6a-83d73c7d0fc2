<!-- Begin Page Content -->
<div class="container-fluid">

    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Progress Proyek</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 bg-transparent p-0">
                <li class="breadcrumb-item"><a href="client.php">Dashboard</a></li>
                <li class="breadcrumb-item active">Progress Proyek</li>
            </ol>
        </nav>
    </div>

    <!-- Info Alert -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="fas fa-info-circle mr-2"></i>
        <strong>Informasi:</strong> Timeline ini menampilkan progress tugas proyek yang sudah disetujui dan sedang dikerjakan.
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <!-- Timeline Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-chart-gantt mr-2"></i>Timeline Progress Proyek
            </h6>
        </div>
        <div class="card-body">
            <?php
            require '../koneksi.php';
            
            // Get approved tasks ordered by date
            $sql = mysqli_query($koneksi, "
                SELECT * FROM tugas_proyek 
                WHERE status_verifikasi = 'approved' 
                ORDER BY tgl ASC
            ");

            if (mysqli_num_rows($sql) > 0) {
                echo '<div class="timeline">';
                
                while ($data = mysqli_fetch_array($sql)) {
                    // Determine status styling
                    $status_class = 'secondary';
                    $status_icon = 'fas fa-clock';
                    $status_text = 'Belum Diatur';
                    
                    if ($data['status'] == 'proses') {
                        $status_class = 'warning';
                        $status_icon = 'fas fa-spinner';
                        $status_text = 'Dalam Proses';
                    } else if ($data['status'] == 'selesai') {
                        $status_class = 'success';
                        $status_icon = 'fas fa-check-circle';
                        $status_text = 'Selesai';
                    } else if ($data['status'] == 'batal') {
                        $status_class = 'danger';
                        $status_icon = 'fas fa-times-circle';
                        $status_text = 'Dibatalkan';
                    }
                    
                    // Calculate days from today
                    $task_date = strtotime($data['tgl']);
                    $today = strtotime(date('Y-m-d'));
                    $days_diff = floor(($task_date - $today) / (60 * 60 * 24));
                    
                    $date_class = '';
                    if ($days_diff < 0) {
                        $date_class = 'text-muted'; // Past
                    } else if ($days_diff == 0) {
                        $date_class = 'text-primary font-weight-bold'; // Today
                    } else {
                        $date_class = 'text-info'; // Future
                    }
            ?>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-<?php echo $status_class; ?>">
                            <i class="<?php echo $status_icon; ?> text-white"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="card border-left-<?php echo $status_class; ?>">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-lg-8 col-md-12">
                                            <h6 class="font-weight-bold text-<?php echo $status_class; ?>">
                                                <?php echo htmlspecialchars($data['nama_kegiatan']); ?>
                                            </h6>
                                            <p class="text-gray-700 mb-2">
                                                <?php echo htmlspecialchars($data['deskripsi']); ?>
                                            </p>
                                            <div class="d-flex flex-wrap align-items-center">
                                                <span class="badge badge-<?php echo $status_class; ?> mr-2 mb-1">
                                                    <?php echo $status_text; ?>
                                                </span>
                                                <?php if ($data['tanggal_verifikasi']): ?>
                                                <small class="text-muted mb-1">
                                                    <i class="fas fa-check mr-1"></i>
                                                    Disetujui: <?php echo date('d M Y', strtotime($data['tanggal_verifikasi'])); ?>
                                                </small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-lg-4 col-md-12 text-lg-right text-left mt-lg-0 mt-2">
                                            <div class="<?php echo $date_class; ?>">
                                                <i class="fas fa-calendar-alt mr-1"></i>
                                                <?php echo date('d M Y', strtotime($data['tgl'])); ?>
                                            </div>
                                            <small class="text-muted">
                                                <?php
                                                if ($days_diff < 0) {
                                                    echo abs($days_diff) . ' hari yang lalu';
                                                } else if ($days_diff == 0) {
                                                    echo 'Hari ini';
                                                } else {
                                                    echo $days_diff . ' hari lagi';
                                                }
                                                ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            <?php
                }
                echo '</div>';
            } else {
            ?>
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">Belum Ada Tugas yang Disetujui</h5>
                    <p class="text-muted">Timeline akan muncul setelah ada tugas yang disetujui oleh admin.</p>
                </div>
            <?php } ?>
        </div>
    </div>

    <!-- Progress Summary -->
    <div class="row">
        <?php
        // Get statistics for progress summary
        $stats_query = mysqli_query($koneksi, "
            SELECT 
                status,
                COUNT(*) as total
            FROM tugas_proyek 
            WHERE status_verifikasi = 'approved'
            GROUP BY status
        ");
        
        $stats = [
            'proses' => 0,
            'selesai' => 0,
            'batal' => 0
        ];
        
        while ($stat = mysqli_fetch_array($stats_query)) {
            $stats[$stat['status']] = $stat['total'];
        }
        
        $total_tasks = array_sum($stats);
        ?>
        
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Dalam Proses</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['proses']; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-spinner fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Selesai</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['selesai']; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Dibatalkan</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['batal']; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<!-- /.container-fluid -->

<style>
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
    padding-left: 70px;
}

.timeline-marker {
    position: absolute;
    left: 15px;
    top: 10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.timeline-content {
    position: relative;
}

@media (max-width: 768px) {
    .timeline::before {
        left: 15px;
    }
    
    .timeline-item {
        padding-left: 50px;
    }
    
    .timeline-marker {
        left: 0;
        width: 25px;
        height: 25px;
    }
}
</style>
