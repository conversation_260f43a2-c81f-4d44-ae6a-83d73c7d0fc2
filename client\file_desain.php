<!-- Begin Page Content -->
<div class="container-fluid">

    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">File Desain Disetujui</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 bg-transparent p-0">
                <li class="breadcrumb-item"><a href="client.php">Dashboard</a></li>
                <li class="breadcrumb-item active">File Desain</li>
            </ol>
        </nav>
    </div>

    <!-- Info Alert -->
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle mr-2"></i>
        <strong>Informasi:</strong> Halaman ini menampilkan file desain yang sudah melalui proses verifikasi dan disetujui untuk proyek Anda.
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <!-- File Gallery -->
    <div class="row">
        <?php
        require '../koneksi.php';
        require_once '../includes/session_manager.php';

        // Get current client ID
        $client_id = get_user_id();

        // Join dengan tabel petugas untuk mendapatkan nama verifikator
        $sql = mysqli_query($koneksi, "
            SELECT fg.*, p.nama_petugas
            FROM file_gambar fg
            LEFT JOIN petugas p ON fg.verifikator_id = p.id_petugas
            WHERE fg.status_verifikasi = 'approved'
            AND (fg.client_id = '$client_id' OR fg.client_id IS NULL)
            ORDER BY fg.tanggal_verifikasi DESC
        ");

        if (mysqli_num_rows($sql) > 0) {
            while ($data = mysqli_fetch_array($sql)) {
                // Get file extension for icon and type
                $file_ext = strtolower(pathinfo($data['gambar'], PATHINFO_EXTENSION));
                $file_icon = 'fas fa-file';
                $file_type = 'File';
                $is_image = false;
                
                if (in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])) {
                    $file_icon = 'fas fa-file-image';
                    $file_type = 'Gambar';
                    $is_image = true;
                } elseif ($file_ext == 'pdf') {
                    $file_icon = 'fas fa-file-pdf';
                    $file_type = 'PDF';
                } elseif (in_array($file_ext, ['dwg', 'obj', 'stl'])) {
                    $file_icon = 'fas fa-cube';
                    $file_type = 'Desain 3D';
                } elseif (in_array($file_ext, ['doc', 'docx'])) {
                    $file_icon = 'fas fa-file-word';
                    $file_type = 'Dokumen';
                }
        ?>
        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="<?php echo $file_icon; ?> mr-2"></i><?php echo $file_type; ?>
                    </h6>
                    <span class="badge badge-success">Disetujui</span>
                </div>
                <div class="card-body">
                    <?php if ($is_image): ?>
                    <div class="text-center mb-3">
                        <img src="../file_proyek/<?php echo $data['gambar']; ?>" 
                             class="img-fluid rounded shadow-sm" 
                             style="max-height: 200px; object-fit: cover;"
                             alt="<?php echo htmlspecialchars($data['deskripsi']); ?>">
                    </div>
                    <?php else: ?>
                    <div class="text-center mb-3 py-4">
                        <i class="<?php echo $file_icon; ?> fa-4x text-primary"></i>
                    </div>
                    <?php endif; ?>
                    
                    <h6 class="font-weight-bold text-gray-800 mb-2">
                        <?php echo htmlspecialchars($data['gambar']); ?>
                    </h6>
                    
                    <p class="text-gray-700 mb-3">
                        <?php echo htmlspecialchars($data['deskripsi']); ?>
                    </p>
                    
                    <div class="row text-sm text-muted mb-3">
                        <div class="col-sm-6 col-12 mb-2 mb-sm-0">
                            <strong>Upload:</strong><br>
                            <?php echo date('d M Y', strtotime($data['tanggal_submit'])); ?>
                        </div>
                        <div class="col-sm-6 col-12">
                            <strong>Disetujui:</strong><br>
                            <?php echo date('d M Y', strtotime($data['tanggal_verifikasi'])); ?>
                        </div>
                    </div>
                    
                    <div class="text-center mb-3">
                        <small class="text-success">
                            <i class="fas fa-user-check mr-1"></i>
                            Disetujui oleh: <?php echo htmlspecialchars($data['nama_petugas'] ?? 'Admin'); ?>
                        </small>
                    </div>
                    
                    <?php if ($data['catatan_verifikasi']): ?>
                    <div class="alert alert-info py-2 mb-3">
                        <small>
                            <strong>Catatan:</strong><br>
                            <?php echo htmlspecialchars($data['catatan_verifikasi']); ?>
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="btn-group-vertical btn-group w-100 d-sm-flex" role="group">
                        <a href="../file_proyek/<?php echo $data['gambar']; ?>" target="_blank"
                           class="btn btn-outline-primary btn-sm mb-1 mb-sm-0">
                            <i class="fas fa-eye"></i> <span class="d-none d-sm-inline">Lihat</span>
                        </a>
                        <a href="../file_proyek/<?php echo $data['gambar']; ?>" download
                           class="btn btn-outline-success btn-sm mb-1 mb-sm-0">
                            <i class="fas fa-download"></i> <span class="d-none d-sm-inline">Download</span>
                        </a>
                        <?php if ($is_image): ?>
                        <button type="button" class="btn btn-outline-info btn-sm"
                                data-toggle="modal" data-target="#imageModal<?php echo $data['id']; ?>">
                            <i class="fas fa-expand"></i> <span class="d-none d-sm-inline">Zoom</span>
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($is_image): ?>
        <!-- Image Modal -->
        <div class="modal fade" id="imageModal<?php echo $data['id']; ?>" tabindex="-1" role="dialog" 
             aria-labelledby="imageModalLabel<?php echo $data['id']; ?>" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="imageModalLabel<?php echo $data['id']; ?>">
                            <?php echo htmlspecialchars($data['gambar']); ?>
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="../file_proyek/<?php echo $data['gambar']; ?>" 
                             class="img-fluid" 
                             alt="<?php echo htmlspecialchars($data['deskripsi']); ?>">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                        <a href="../file_proyek/<?php echo $data['gambar']; ?>" download
                           class="btn btn-primary">
                            <i class="fas fa-download"></i> Download
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php
            }
        } else {
        ?>
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-folder-open fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">Belum Ada File Desain yang Disetujui</h5>
                    <p class="text-muted">File desain akan muncul di sini setelah disetujui oleh admin.</p>
                </div>
            </div>
        </div>
        <?php } ?>
    </div>

    <!-- File Statistics -->
    <div class="row mt-4">
        <?php
        // Get file statistics for this client
        $total_files = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM file_gambar WHERE status_verifikasi = 'approved' AND (client_id = '$client_id' OR client_id IS NULL)");
        $total_approved_files = mysqli_fetch_array($total_files)['total'];

        $image_files = mysqli_query($koneksi, "
            SELECT COUNT(*) as total FROM file_gambar
            WHERE status_verifikasi = 'approved'
            AND (client_id = '$client_id' OR client_id IS NULL)
            AND (gambar LIKE '%.jpg' OR gambar LIKE '%.jpeg' OR gambar LIKE '%.png' OR gambar LIKE '%.gif')
        ");
        $total_image_files = mysqli_fetch_array($image_files)['total'];

        $pdf_files = mysqli_query($koneksi, "
            SELECT COUNT(*) as total FROM file_gambar
            WHERE status_verifikasi = 'approved'
            AND (client_id = '$client_id' OR client_id IS NULL)
            AND gambar LIKE '%.pdf'
        ");
        $total_pdf_files = mysqli_fetch_array($pdf_files)['total'];

        $design_files = mysqli_query($koneksi, "
            SELECT COUNT(*) as total FROM file_gambar
            WHERE status_verifikasi = 'approved'
            AND (client_id = '$client_id' OR client_id IS NULL)
            AND (gambar LIKE '%.dwg' OR gambar LIKE '%.obj' OR gambar LIKE '%.stl')
        ");
        $total_design_files = mysqli_fetch_array($design_files)['total'];
        ?>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total File</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_approved_files; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                File Gambar</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_image_files; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-image fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                File PDF</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_pdf_files; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-pdf fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                File Desain 3D</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_design_files; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cube fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<!-- /.container-fluid -->
