<?php
/**
 * <PERSON>aman Client - Dashboard Content
 *
 * Note: Session validation is handled by client.php that includes this file.
 * This file assumes session is already validated and $_SESSION data is available.
 */

if (isset($_GET['url']))
{
    $url=$_GET['url'];

    switch($url)
    {
        case 'progress_proyek':
            include 'progress_proyek.php';
            break;

        case 'file_desain':
            include 'file_desain.php';
            break;

        default:
            // Redirect ke dashboard jika URL tidak dikenali
            echo "<script>window.location='client.php';</script>";
            break;
    }
}
else
{
    ?>
    <!-- Dashboard Content -->
    <div class="row">
        <div class="col-xl-12 col-md-12 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Selamat Datang
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php
                                // Safety check for session data
                                echo isset($_SESSION['nama']) && !empty($_SESSION['nama']) ? $_SESSION['nama'] : 'Client';
                                ?> - Client Antosa Arsitek
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-xl-6 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Lihat Progress Proyek
                            </div>
                            <div class="text-gray-600">
                                Pantau perkembangan proyek Anda dalam bentuk timeline
                            </div>
                        </div>
                        <div class="col-auto">
                            <a href="client.php?url=progress_proyek" class="btn btn-info btn-sm">
                                <i class="fas fa-chart-gantt"></i> Lihat Progress
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                File Desain Approved
                            </div>
                            <div class="text-gray-600">
                                Lihat dan download file desain yang sudah disetujui
                            </div>
                        </div>
                        <div class="col-auto">
                            <a href="client.php?url=file_desain" class="btn btn-success btn-sm">
                                <i class="fas fa-file-image"></i> Lihat File
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <?php
        require '../koneksi.php';
        
        // Count approved tasks
        $approved_tasks = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM tugas_proyek WHERE status_verifikasi = 'approved'");
        $total_approved_tasks = mysqli_fetch_array($approved_tasks)['total'];
        
        // Count approved files
        $approved_files = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM file_gambar WHERE status_verifikasi = 'approved'");
        $total_approved_files = mysqli_fetch_array($approved_files)['total'];
        
        // Count completed tasks
        $completed_tasks = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM tugas_proyek WHERE status = 'selesai' AND status_verifikasi = 'approved'");
        $total_completed_tasks = mysqli_fetch_array($completed_tasks)['total'];
        
        // Count in progress tasks
        $progress_tasks = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM tugas_proyek WHERE status = 'proses' AND status_verifikasi = 'approved'");
        $total_progress_tasks = mysqli_fetch_array($progress_tasks)['total'];
        ?>
        
        <!-- Total Tugas Approved -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Tugas Disetujui</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_approved_tasks; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tasks fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total File Approved -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                File Desain Disetujui</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_approved_files; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-image fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tugas Selesai -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Tugas Selesai</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_completed_tasks; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tugas Dalam Proses -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Tugas Dalam Proses</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_progress_tasks; ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-spinner fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}
?>
